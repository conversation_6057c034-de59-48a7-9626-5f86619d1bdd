import { IMinioBucketConfig } from '@/interfaces/minio.interface'
import path from 'path'
/*
* <PERSON><PERSON><PERSON> nghĩa các hằng số chung cho toàn bộ ứng dụng
*/
export default {
  // Đ<PERSON><PERSON> nghĩa các môi trường
  NODE: {
    DEV: 'dev',
    PROD: 'prod',
  },
  // Đ<PERSON>nh nghĩa các bucket cho minio
  MINIO: {
    BUCKETS: [
      { name: 'public-image', private: false },
      { name: 'private-image', private: true },
      // Add more buckets here if needed (ex: video, audio, ...)
    ] as IMinioBucketConfig[],
  },
  // Định nghĩa các folder cho ứng dụng
  FOLDER: {
    TEMPLATE_DIR: path.resolve('src/templates'),
    LOGS_DIR: path.resolve('logs'),
    API_LOGS_DIR: path.resolve('logs/apis'),
    ERROR_LOGS_DIR: path.resolve('logs/errors'),
    BACKUP_DIR: path.resolve('backups'),
  },
  //PAGINATION
  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_LIMIT: 10,
  },
  MULTER: {
    AVATAR: "avatar",
  },
  COOKIES: {
    REFRESH_TOKEN_NAME: 'refresh_token',
  },
} as const
