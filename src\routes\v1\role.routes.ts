import { Router } from 'express'
import * as RoleController from '@/controllers/role.controller'
import { paramsId } from '@/validators/common.validator'
import { validateBody, validateParams } from '@/middlewares/validator.middleware'
import * as RoleValidator from '@/validators/role.validator'

const router = Router()

router.get('/', RoleController.getPaginate)

router.get('/all', RoleController.getAll)

router.get('/:id', validateParams(paramsId), RoleController.getById)

router.post(
  '/',
  validateBody(RoleValidator.create),
  RoleController.create
)

router.put(
  '/:id',
  validateParams(paramsId),
  validateBody(RoleValidator.update),
  RoleController.update
)

router.delete(
  '/:id/hard',
  validateParams(paramsId),
  RoleController.hardDelete
)

export default router
