import * as PermissionController from '@/controllers/permission.controller'
import { Router } from 'express'
import { paramsId } from '@/validators/common.validator'
import {
  validateBody,
  validateParams,
} from '@/middlewares/validator.middleware'
import * as PermissionValidator from '@/validators/permission.validator'

const router = Router()

router.get('/', PermissionController.getPaginate)

router.get('/all', PermissionController.getAll)

router.get('/:id', validateParams(paramsId), PermissionController.getById)

router.post(
  '/',
  validateBody(PermissionValidator.create),
  PermissionController.create
)

router.put(
  '/:id',
  validateParams(paramsId),
  validateBody(PermissionValidator.update),
  PermissionController.update
)

router.delete(
  '/:id/hard',
  validateParams(paramsId),
  PermissionController.hardDelete
)

export default router
