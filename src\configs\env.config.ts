import dotenv from 'dotenv'

dotenv.config({ path: `.env.${process.env.NODE_ENV}` })

export default {
  // Basic info about the server
  NODE_ENV: process.env.NODE_ENV || 'dev',
  PORT: process.env.PORT || 8888,
  NAME: process.env.NAME || 'Express TS App',
  CORS_ORIGIN: (process.env.CORS_ORIGIN || '*').split(','),

  // Database config
  DB: {
    MONGO_URI: process.env.MONGO_URI,
    MONGO_USERNAME: process.env.MONGO_USERNAME,
    MONGO_PASSWORD: process.env.MONGO_PASSWORD,
    MONGO_DATABASE: process.env.MONGO_DATABASE,
  },
  // Minio config
  MINIO: {
    ENDPOINT: process.env.MINIO_ENDPOINT || 'http://localhost:9000',
    ACCESS_KEY: process.env.MINIO_ACCESS_KEY || 'minio_ac',
    SECRET_KEY: process.env.MINIO_SECRET_KEY || 'minio_se',
    HOST: process.env.MINIO_HOST || 'http://localhost',
    PORT: Number(process.env.MINIO_PORT) || 9000,
  },
  // Redis config
  REDIS: {
    PASSWORD: process.env.REDIS_PASSWORD,
    REDIS_HOST: process.env.REDIS_HOST,
    REDIS_PORT: Number(process.env.REDIS_PORT) || 6379,
  },
  //NodeMailer config
  NODEMAILER: {
    SENDER: process.env.NODEMAILER_SENDER as string,
    APP_PASSWORD: process.env.NODEMAILER_APP_PASSWORD,
    HOST: process.env.NODEMAILER_HOST,
    ADMIN_EMAIL: process.env.NODEMAILER_ADMIN_EMAIL,
    LOG_SENDER: process.env.NODEMAILER_LOG_SENDER
  },
  // JWT config
  JWT: {
    // Secret
    ACCESS_TOKEN_SECRET: process.env.ACCESS_TOKEN_SECRET,
    REFRESH_TOKEN_SECRET: process.env.REFRESH_TOKEN_SECRET,
    PRE_ACCESS_TOKEN_SECRET: process.env.PRE_ACCESS_TOKEN_SECRET,
    // Expire time
    ACCESS_TOKEN_EXPIRE: process.env.ACCESS_TOKEN_EXPIRE,
    REFRESH_TOKEN_EXPIRE: process.env.REFRESH_TOKEN_EXPIRE,
    PRE_ACCESS_TOKEN_EXPIRE: process.env.PRE_ACCESS_TOKEN_EXPIRE,
    // Hash secret
    TOKEN_HASH_SECRET: process.env.TOKEN_HASH_SECRET,
  },
} as const
