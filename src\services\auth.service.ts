import User from '@/models/user.model'
import AppError from '@/utils/app-error.util'
import { StatusCodes } from 'http-status-codes'
import USER_MESSAGE from '@/messages/user.message'
import { successResponse } from '@/utils/response.util'
import {
  IUpdate2FA,
  IUpdateMe,
  IUpdatePassword,
} from '@/interfaces/models/user.interface'
import { comparePassword, hashPassword } from '@/utils/encrypt.util'
import CONSTANT from '@/configs/constant.config'
import { uploadFile } from '@/providers/minio.provider'
import { EFileType } from '@/configs/enum.config'
import { ILogout } from '@/interfaces/auth.interface'

const getProfile = (id: string) => {
  return User.findById(id, {
    password: 0,
    status: 0,
    resetPasswordToken: 0,
    resetPasswordExpire: 0,
    verifyEmailToken: 0,
    verifyEmailExpire: 0,
    fcmTokens: 0,
  })
}

export const logoutOnWeb = async (id: string, refreshToken: string, fcmToken?: string) => {
  return successResponse({}, 'Logout success')
}

export const logoutOnMobile = async (id: string, clientId: string, fcmToken?: string) => {
  return successResponse({}, 'Logout success')
}

export const getMe = async (id: string) => {
  const user = await getProfile(id)
  if (!user) {
    throw new AppError(StatusCodes.NOT_FOUND, USER_MESSAGE.NOT_FOUND)
  }
  return successResponse(user, USER_MESSAGE.GET_BY_ID_SUCCESS)
}

export const updateMe = async (id: string, data: IUpdateMe) => {
  const user = await User.findByIdAndUpdate(id, data, { new: true })
  if (!user) {
    throw new AppError(StatusCodes.NOT_FOUND, USER_MESSAGE.NOT_FOUND)
  }

  const profile = await getProfile(id)

  return successResponse(profile, USER_MESSAGE.UPDATE_SUCCESS)
}

export const toggle2FA = async (id: string, data: IUpdate2FA) => {
  const user = await User.findByIdAndUpdate(
    id,
    { is2FAEnabled: data.is2FAEnabled },
    { new: true }
  )
  if (!user) {
    throw new AppError(StatusCodes.NOT_FOUND, USER_MESSAGE.NOT_FOUND)
  }

  const profile = await getProfile(id)

  return successResponse(profile, USER_MESSAGE.UPDATE_SUCCESS)
}

export const updatePassword = async (id: string, data: IUpdatePassword) => {
  const user = await User.findById(id)
  if (!user) {
    throw new AppError(StatusCodes.NOT_FOUND, USER_MESSAGE.NOT_FOUND)
  }

  const isMatch = await comparePassword(data.oldPassword, user.password)
  if (!isMatch) {
    throw new AppError(StatusCodes.BAD_REQUEST, USER_MESSAGE.OLD_PASSWORD_INCORRECT)
  }

  const newPassword = await hashPassword(data.newPassword)
  user.password = newPassword
  await user.save()

  const profile = await getProfile(id)

  return successResponse(profile, USER_MESSAGE.UPDATE_SUCCESS)
}

export const updateAvatar = async (id: string, file: Express.Multer.File) => {
  if (!file) {
    throw new AppError(StatusCodes.BAD_REQUEST, USER_MESSAGE.FILE_NOT_FOUND)
  }
  const user = await User.findById(id)
  if (!user) {
    throw new AppError(StatusCodes.NOT_FOUND, USER_MESSAGE.NOT_FOUND)
  }

  const fileResponse = await uploadFile({
    reqFile: file,
    bucket: CONSTANT.MINIO.BUCKETS[0],
    type: EFileType.IMAGE,
  })

  user.avatar = fileResponse.url
  await user.save()

  const profile = await getProfile(id)

  return successResponse(profile, USER_MESSAGE.UPDATE_SUCCESS)
}
