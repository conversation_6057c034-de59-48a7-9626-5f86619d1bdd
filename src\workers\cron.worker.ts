/* eslint-disable no-console */
import connectDB from '@/configs/mongoose.config'
import cron from 'node-cron'
import { sendMailLogs } from '@/providers/nodemailer.provider'

connectDB()
  .then(() => {
    console.log('[Cron Worker]: Connected to database')
  })
  .catch(error => {
    console.log('[Cron Worker]: Error starting server', error)
    process.exit(1)
  })

cron.schedule(
  '0 22 * * *',
  async () => {
    await sendMailLogs()
  },
  {
    timezone: 'Asia/Ho_Chi_Minh',
  }
)

