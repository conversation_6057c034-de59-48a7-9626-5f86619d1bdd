{"name": "ts-rest-express-boilerplate", "version": "1.0.0", "description": "A boilerplate for typescript REST API with Express", "keywords": ["boilerplate", "express", "rest", "typescript"], "engines": {"node": ">=22.13.0"}, "license": "MIT", "author": "<PERSON><PERSON><PERSON> (Horob1) <github.com/Horob1>", "main": "./src/index.ts", "scripts": {"test": "jest", "test:watch": "jest --watch", "build": "tsc && tsc-alias", "lint": "eslint", "lint:fix": "eslint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "dev": "cross-env NODE_ENV=dev nodemon --exec ts-node -r tsconfig-paths/register ./src/index.ts", "dev:cron": "cross-env NODE_ENV=dev nodemon --exec ts-node -r tsconfig-paths/register ./src/workers/cron.worker.ts", "dev:queue": "cross-env NODE_ENV=dev nodemon --exec ts-node -r tsconfig-paths/register ./src/workers/queue.worker.ts", "start": "cross-env NODE_ENV=prod node ./dist/index.js", "start:cron": "cross-env NODE_ENV=prod node ./dist/workers/cron.worker.js", "start:queue": "cross-env NODE_ENV=prod node ./dist/workers/queue.worker.js", "pm:start": "pm2 start ecosystem.config.js", "pm:stop": "pm2 stop ecosystem.config.js", "pm:delete": "pm2 delete ecosystem.config.js"}, "devDependencies": {"@eslint/js": "^9.24.0", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.16", "@types/mime-types": "^3.0.1", "@types/morgan": "^1.9.9", "@types/ms": "^2.1.0", "@types/multer": "^1.4.12", "@types/node": "^22.14.1", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/pug": "^2.0.10", "@types/qrcode": "^1.5.5", "@types/sanitize-html": "^2.15.0", "@types/streamifier": "^0.1.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-prettier": "^5.2.6", "globals": "^16.0.0", "jest": "^29.7.0", "nodemon": "^3.1.9", "prettier": "^3.5.3", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.29.1"}, "dependencies": {"@firebase/firestore": "^4.7.12", "@google/genai": "^0.13.0", "@types/fs-extra": "^11.0.4", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "exit-hook": "^4.0.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "firebase": "^11.7.1", "firebase-admin": "^13.3.0", "fs-extra": "^11.3.0", "helmet": "^8.1.0", "http-status-codes": "^2.3.0", "joi": "^17.10.2", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "minio": "^8.0.5", "mongoose": "^8.13.2", "morgan": "^1.10.0", "ms": "^2.1.3", "multer": "^1.4.5-lts.2", "node-cron": "^3.0.3", "nodemailer": "^6.10.1", "otplib": "^12.0.1", "playwright": "^1.52.0", "pug": "^3.0.3", "qrcode": "^1.5.4", "redis": "^4.7.0", "sanitize-html": "^2.16.0", "sharp": "^0.34.2", "socket.io": "^4.8.1", "streamifier": "^0.1.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ua-parser-js": "^2.0.3", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}}