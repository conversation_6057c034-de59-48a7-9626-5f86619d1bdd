{"$schema": "https://json.schemastore.org/tsconfig", "display": "Node 23", "_version": "23.0.0", "compileOnSave": false, "compilerOptions": {"lib": ["es2024"], "module": "nodenext", "target": "es2024", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "moduleResolution": "node16", "typeRoots": ["node_modules/@types"], "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true, "pretty": true, "sourceMap": true, "declaration": true, "outDir": "dist", "rootDir": "./src", "allowJs": true, "noEmit": false, "resolveJsonModule": true, "importHelpers": true, "baseUrl": "./", "paths": {"@/*": ["./src/*"], "@/configs/*": ["./src/configs/*"], "@/controllers/*": ["./src/controllers/*"], "@/messages/*": ["./src/messages/*"], "@/interfaces/*": ["./src/interfaces/*"], "@/socket/*": ["./src/socket/*"], "@/middlewares/*": ["./src/middlewares/*"], "@/models/*": ["./src/models/*"], "@/routes/*": ["./src/routes/*"], "@/services/*": ["./src/services/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/validators/*": ["./src/validators/*"], "@/providers/*": ["./src/providers/*"], "@/workers/*": ["./src/workers/*"], "@/data/*": ["./src/data/*"], "@/templates/*": ["./src/templates/*"]}}, "files": ["src/types.d.ts"], "exclude": ["node_modules", "dist"], "include": ["src/**/*"]}