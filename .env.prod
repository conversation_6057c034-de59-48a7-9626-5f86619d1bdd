# App Config
PORT=8888
NODE_ENV=prod
NAME=your_app_name_here
CORS_ORIGIN=http://localhost:3000,http://localhost:5173,http://localhost:4173

# MongoDB Config
MONGO_USERNAME=admin
MONGO_PASSWORD=your_strong_mongo_password_here
MONGO_DATABASE=your_database_name
MONGO_URI=***********************************************************************************************

# Redis Config
REDIS_PASSWORD=your_strong_redis_password_here
REDIS_HOST=redis
REDIS_PORT=6379

# Minio Config
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=your_strong_minio_password_here
MINIO_ENDPOINT=http://minio:9000
MINIO_HOST=minio
MINIO_PORT=9000

#Nodemailer
NODEMAILER_HOST = smtp.gmail.com
NODEMAILER_APP_PASSWORD = dllk lyye vczv hknw
NODEMAILER_SENDER = <EMAIL>
NODEMAILER_ADMIN_EMAIL = <EMAIL>
NODEMAILER_LOG_SENDER = "The Server Team <3"

#JWT
ACCESS_TOKEN_SECRET=your_strong_access_token_secret_here
REFRESH_TOKEN_SECRET=your_strong_refresh_token_secret_here
PRE_ACCESS_TOKEN_SECRET=your_strong_pre_access_token_secret_here

ACCESS_TOKEN_EXPIRE=15m
REFRESH_TOKEN_EXPIRE=100d
PRE_ACCESS_TOKEN_EXPIRE=15m

TOKEN_HASH_SECRET=your_strong_token_hash_secret_here
