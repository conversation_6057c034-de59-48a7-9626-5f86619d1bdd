# Run: docker compose --env-file .env.prod up --build
services:
  app:
    container_name: express-ts-app
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '8888:8888'
    env_file: .env.prod
    networks:
      - app-network
    environment:
      - NODE_ENV=prod
    depends_on:
      - mongo
      - redis
      - minio
    restart: always
    command: pm2-runtime ecosystem.config.js

  mongo:
    image: mongo:7
    container_name: express-mongo
    ports:
      - '27017:27017'
    volumes:
      - mongo-data:/data/db
    env_file: .env.prod
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
      - MONGO_INITDB_DATABASE=${MONGO_DATABASE}
    restart: always
    networks:
      - app-network

  redis:
    image: redis:7
    container_name: express-redis
    ports:
      - '6379:6379'
    volumes:
      - redis-data:/data
    env_file: .env.prod
    command: redis-server --requirepass ${REDIS_PASSWORD}
    restart: always
    networks:
      - app-network

  minio:
    image: minio/minio:latest
    container_name: express-minio
    ports:
      - '9000:9000'
      - '9001:9001'
    volumes:
      - minio-data:/data
    env_file: .env.prod
    environment:
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_KEY}
    command: server /data --console-address ":9001"
    restart: always
    networks:
      - app-network

volumes:
  mongo-data:
  redis-data:
  minio-data:

networks:
  app-network:
    driver: bridge
