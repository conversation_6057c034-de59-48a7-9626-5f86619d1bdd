import { Router } from 'express'
import AuthMiddleware from '@/middlewares/auth.middleware'
import upload from '@/configs/multer.config'
import CONSTANT from '@/configs/constant.config'
import * as AuthController from '@/controllers/auth.controller'

const router = Router()

//Public routes

// TODO: Login
/**
 * @flow
 * 1. Không có user hoặc sai mật khẩu -> Lỗi: 401
 * 2. Kiểm tra xem user có đang bị khóa không -> Lỗi: 403
 * 3. (Sai 5 lần cùng 1 IP thì khóa tài khoản 10 phút và gửi mail cảnh báo cho user) -> Lỗi: 429
 * 4. Xoá cache số lần đăng nhập sai của user tương ứng với IP
 * 5. User chưa verify email -> Send verify email -> Gửi về: 200 + pre-access-token
 * 6. User bật 2FA -> G<PERSON><PERSON> về: 200 + pre-access-token
 * 7. Tạo access token và refresh token
 * 8. T<PERSON><PERSON> về access token và refresh token theo Platform
 */
router.post('/login', (req, res) => {
  res.send('Login')
})

// TODO: Login with google
/**
 * @flow
 * 1. Kiểm tra user có tồn tại không -> Không tồn tại: Tạo user mới (status = ACTIVE)
 * 2. Kiểm tra status của user -> Banned: Lỗi: 403, Pending: Cập nhật lại status = ACTIVE
 * 3. Tạo access token và refresh token
 * 4. Trả về access token và refresh token theo Platform
 */
router.post('/login/google', (req, res) => {
  res.send('Login with google')
})

// TODO: Register
/**
 * @flow
 * 1. Kiểm tra xem email đã tồn tại chưa -> Lỗi: 409
 * 2. Tạo user mới với status = PENDING
 * 3. Gửi mail verify email
 * 4. Trả về thông báo success cùng với pre-access-token để verify email
 */
router.post('/register', (req, res) => {
  res.send('Register')
})

// TODO: Forgot password
/**
 * @flow
 * 1. Kiểm tra xem email có tồn tại không -> Lỗi: 401
 * 2. Kiểm tra xem user có đang ACTIVE không (Kiểm tra status của user) -> Lỗi: 403
 * 3. Kiểm tra cache xem email đó có đang trong quá trình reset password không -> Lỗi: 429
 * 4. Tạo reset password token
 * 5. Gửi mail reset password
 * 6. Trả về thông báo success cùng với pre-access-token để reset password
 */
router.post('/forgot-password', (req, res) => {
  res.send('Forgot password')
})

// TODO: Refresh token
// Đặt tên là washing để tránh trùng với refresh token (Khó đoán được ý nghĩa giúp tránh bị kẻ xấu xâm nhập)
router.post('/washing', (req, res) => {
  res.send('Refresh token')
})

// Require pre-access-token

// TODO: Resend verify email (Gửi lại email verify, forgot password)
/**
 * @flow
 * 1. Kiểm tra xem pre-access-token có hợp lệ không -> Lỗi: 403
 * 2. Kiểm tra xem pre-access-token còn hạn sử dụng không -> Lỗi: 403
 * ...
 * 4. Sinh lại pre-access-token mới
 */
router.post('/resend', (req, res) => {
  res.send('Resend verify email')
})

// TODO: Reset password
router.post('/reset-password', (req, res) => {
  res.send('Reset password')
})

// TODO: Verify email
router.post('/verify-email', (req, res) => {
  res.send('Verify email')
})

// TODO: Verify 2FA
router.post('/verify-2fa', (req, res) => {
  res.send('Verify 2FA')
})

// Unpublic routes
router.use(AuthMiddleware.auth)

router.post('/logout', (req, res) => {
  res.send('Logout')
})

router.post('/logout-device', (req, res) => {
  res.send('Logout device')
})

router.get('/me', AuthController.getMe)

router.get('/me/devices', (req, res) => {
  res.send('Get my devices')
})

router.put('/me', AuthController.updateMe)

router.put(
  '/me/avatar',
  upload.single(CONSTANT.MULTER.AVATAR),
  AuthController.updateAvatar
)

router.put('/me/2fa', AuthController.toggle2FA)

router.put('/me/password', AuthController.updatePassword)

export default router
