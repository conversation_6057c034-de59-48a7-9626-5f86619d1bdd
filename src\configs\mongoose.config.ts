import ENV from './env.config'
import mongoose from 'mongoose'

export default async () => {
  try {
    await mongoose
      .set('strictQuery', true)
      .connect(ENV.DB.MONGO_URI as string, { retryWrites: true, w: 'majority' })

    //TODO: Init data for first time
    // await initDB()
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('[DB]: Error connecting to the database', error)
  }
}
