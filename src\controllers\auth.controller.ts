import { ParamsDictionary } from 'express-serve-static-core'
import { NextFunction, Request, Response } from 'express'
import { StatusCodes } from 'http-status-codes'
import * as AuthService from '@/services/auth.service'
import {
  IUpdate2FA,
  IUpdateMe,
  IUpdatePassword,
} from '@/interfaces/models/user.interface'
import { ILogout } from '@/interfaces/auth.interface'
import { EPlatform } from '@/configs/enum.config'
import CONSTANT from '@/configs/constant.config'

export const logout = async (
  req: Request<ParamsDictionary, any, ILogout>,
  res: Response,
  next: NextFunction
) => {
  try {
    const userId = req.user?._id as string
    let data
    if (req.platform === EPlatform.WEB) {
      data = await AuthService.logoutOnWeb(
        userId,
        req.cookies[CONSTANT.COOKIES.REFRESH_TOKEN_NAME],
        req.body.fcmToken
      )
    } else {
      data = await AuthService.logoutOnMobile(
        userId,
        req.body.clientId as string,
        req.body.fcmToken
      )
    }

    res.status(StatusCodes.OK).json(data)
  } catch (error) {
    next(error)
  }
}

export const getMe = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const data = AuthService.getMe(req.user?._id as string)
    res.status(StatusCodes.OK).json(data)
  } catch (error) {
    next(error)
  }
}

export const updateMe = async (
  req: Request<ParamsDictionary, any, IUpdateMe>,
  res: Response,
  next: NextFunction
) => {
  try {
    const data = await AuthService.updateMe(req.user?._id as string, req.body)
    res.status(StatusCodes.OK).json(data)
  } catch (error) {
    next(error)
  }
}

export const updateAvatar = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const data = await AuthService.updateAvatar(
      req.user?._id as string,
      req.file as Express.Multer.File
    )
    res.status(StatusCodes.OK).json(data)
  } catch (error) {
    next(error)
  }
}

export const updatePassword = async (
  req: Request<ParamsDictionary, any, IUpdatePassword>,
  res: Response,
  next: NextFunction
) => {
  try {
    const data = await AuthService.updatePassword(
      req.user?._id as string,
      req.body
    )
    res.status(StatusCodes.OK).json(data)
  } catch (error) {
    next(error)
  }
}

export const toggle2FA = async (
  req: Request<ParamsDictionary, any, IUpdate2FA>,
  res: Response,
  next: NextFunction
) => {
  try {
    const data = await AuthService.toggle2FA(req.user?._id as string, req.body)
    res.status(StatusCodes.OK).json(data)
  } catch (error) {
    next(error)
  }
}
