# App Config
PORT=8888
NODE_ENV=dev
NAME=Express_TS_App
CORS_ORIGIN=http://localhost:3000,http://localhost:5173,http://localhost:4173


# MongoDB Config
MONGO_USERNAME=horob1
MONGO_PASSWORD=ILUKn5cnbaOhYOva
MONGO_DATABASE=base_be
MONGO_URI=mongodb+srv://horob1:<EMAIL>/base_be

# Redis Config (dev)
REDIS_PASSWORD=your_strong_redis_password_here
REDIS_HOST=localhost
REDIS_PORT=6379

# Minio Config (dev)
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=your_strong_minio_password_here
MINIO_ENDPOINT=http://localhost:9000
MINIO_HOST=localhost
MINIO_PORT=9000

#Nodemailer(dev)
NODEMAILER_HOST = smtp.gmail.com
NODEMAILER_APP_PASSWORD = dllk lyye vczv hknw
NODEMAILER_SENDER = <EMAIL>
NODEMAILER_ADMIN_EMAIL = <EMAIL>
NODEMAILER_LOG_SENDER = "The Server Team <3"

#JWT
ACCESS_TOKEN_SECRET=your_strong_access_token_secret_here
REFRESH_TOKEN_SECRET=your_strong_refresh_token_secret_here
PRE_ACCESS_TOKEN_SECRET=your_strong_pre_access_token_secret_here

ACCESS_TOKEN_EXPIRE=15m
REFRESH_TOKEN_EXPIRE=100d
PRE_ACCESS_TOKEN_EXPIRE=15m

TOKEN_HASH_SECRET=your_strong_token_hash_secret_here
